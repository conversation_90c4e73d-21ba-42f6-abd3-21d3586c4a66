'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, Inquiry } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';

export default function InquiriesView() {
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null);
  const [filter, setFilter] = useState<'all' | 'new' | 'read' | 'responded' | 'closed'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'contact' | 'service'>('all');
  const { isAdmin } = useAuth();

  useEffect(() => {
    if (isAdmin) {
      loadInquiries();
    }
  }, [isAdmin]);

  const loadInquiries = async () => {
    try {
      setLoading(true);
      const data = await db.getInquiries();
      setInquiries(data);
    } catch (err) {
      console.error('Error loading inquiries:', err);
      setError('Failed to load inquiries');
    } finally {
      setLoading(false);
    }
  };

  const updateInquiryStatus = async (inquiryId: string, status: Inquiry['status']) => {
    try {
      const result = await db.updateInquiry(inquiryId, { status });
      if (result) {
        setInquiries(prev => 
          prev.map(inquiry => 
            inquiry.id === inquiryId ? { ...inquiry, ...result } : inquiry
          )
        );
        if (selectedInquiry?.id === inquiryId) {
          setSelectedInquiry(result);
        }
      }
    } catch (err) {
      console.error('Error updating inquiry:', err);
      setError('Failed to update inquiry status');
    }
  };

  const updateInquiryPriority = async (inquiryId: string, priority: Inquiry['priority']) => {
    try {
      const result = await db.updateInquiry(inquiryId, { priority });
      if (result) {
        setInquiries(prev => 
          prev.map(inquiry => 
            inquiry.id === inquiryId ? { ...inquiry, ...result } : inquiry
          )
        );
        if (selectedInquiry?.id === inquiryId) {
          setSelectedInquiry(result);
        }
      }
    } catch (err) {
      console.error('Error updating inquiry priority:', err);
      setError('Failed to update inquiry priority');
    }
  };

  const deleteInquiry = async (inquiryId: string) => {
    if (!window.confirm('Are you sure you want to delete this inquiry?')) return;

    try {
      const success = await db.deleteInquiry(inquiryId);
      if (success) {
        setInquiries(prev => prev.filter(inquiry => inquiry.id !== inquiryId));
        if (selectedInquiry?.id === inquiryId) {
          setSelectedInquiry(null);
        }
      }
    } catch (err) {
      console.error('Error deleting inquiry:', err);
      setError('Failed to delete inquiry');
    }
  };

  const filteredInquiries = inquiries.filter(inquiry => {
    if (filter !== 'all' && inquiry.status !== filter) return false;
    if (typeFilter !== 'all' && inquiry.type !== typeFilter) return false;
    return true;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'read': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'responded': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  if (!isAdmin) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading inquiries...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Contact Inquiries ({filteredInquiries.length})
        </h2>
        <button
          onClick={loadInquiries}
          className="mt-4 sm:mt-0 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Status Filter</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="read">Read</option>
            <option value="responded">Responded</option>
            <option value="closed">Closed</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Type Filter</label>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="contact">Contact</option>
            <option value="service">Service</option>
          </select>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Inquiries Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Inquiries List */}
        <div className="space-y-4">
          {filteredInquiries.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No inquiries found matching the current filters.
            </div>
          ) : (
            filteredInquiries.map((inquiry) => (
              <motion.div
                key={inquiry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                  selectedInquiry?.id === inquiry.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => setSelectedInquiry(inquiry)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {inquiry.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {inquiry.email}
                    </p>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(inquiry.status)}`}>
                      {inquiry.status}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(inquiry.priority)}`}>
                      {inquiry.priority}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span className="capitalize">{inquiry.type}</span>
                  <span>{new Date(inquiry.created_at).toLocaleDateString()}</span>
                </div>
                
                {inquiry.subject && (
                  <p className="mt-2 text-sm text-gray-700 dark:text-gray-300 truncate">
                    {inquiry.subject}
                  </p>
                )}
              </motion.div>
            ))
          )}
        </div>

        {/* Inquiry Details */}
        <div className="lg:sticky lg:top-4">
          {selectedInquiry ? (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedInquiry.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {selectedInquiry.email}
                  </p>
                  {selectedInquiry.phone && (
                    <p className="text-gray-600 dark:text-gray-400">
                      {selectedInquiry.phone}
                    </p>
                  )}
                </div>
                <button
                  onClick={() => setSelectedInquiry(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ✕
                </button>
              </div>

              {/* Inquiry Details */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Type</label>
                  <span className="capitalize text-gray-900 dark:text-white">
                    {selectedInquiry.type}
                  </span>
                </div>

                {selectedInquiry.company && (
                  <div>
                    <label className="block text-sm font-medium mb-1">Company</label>
                    <span className="text-gray-900 dark:text-white">
                      {selectedInquiry.company}
                    </span>
                  </div>
                )}

                {selectedInquiry.subject && (
                  <div>
                    <label className="block text-sm font-medium mb-1">Subject</label>
                    <span className="text-gray-900 dark:text-white">
                      {selectedInquiry.subject}
                    </span>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium mb-1">Message</label>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                    <p className="text-gray-900 dark:text-white whitespace-pre-wrap">
                      {selectedInquiry.message}
                    </p>
                  </div>
                </div>

                {selectedInquiry.type === 'service' && (
                  <>
                    {selectedInquiry.service_type && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Service Type</label>
                        <span className="text-gray-900 dark:text-white">
                          {selectedInquiry.service_type}
                        </span>
                      </div>
                    )}
                    
                    {selectedInquiry.budget_range && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Budget Range</label>
                        <span className="text-gray-900 dark:text-white">
                          {selectedInquiry.budget_range}
                        </span>
                      </div>
                    )}
                    
                    {selectedInquiry.timeline && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Timeline</label>
                        <span className="text-gray-900 dark:text-white">
                          {selectedInquiry.timeline}
                        </span>
                      </div>
                    )}
                  </>
                )}

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div>
                    <label className="block text-sm font-medium mb-2">Status</label>
                    <select
                      value={selectedInquiry.status}
                      onChange={(e) => updateInquiryStatus(selectedInquiry.id, e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800"
                    >
                      <option value="new">New</option>
                      <option value="read">Read</option>
                      <option value="responded">Responded</option>
                      <option value="closed">Closed</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Priority</label>
                    <select
                      value={selectedInquiry.priority}
                      onChange={(e) => updateInquiryPriority(selectedInquiry.id, e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                </div>

                <div className="flex space-x-3 pt-4">
                  <a
                    href={`mailto:${selectedInquiry.email}?subject=Re: ${selectedInquiry.subject || 'Your inquiry'}`}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center"
                  >
                    Reply via Email
                  </a>
                  <button
                    onClick={() => deleteInquiry(selectedInquiry.id)}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Delete
                  </button>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p>Created: {new Date(selectedInquiry.created_at).toLocaleString()}</p>
                  <p>Updated: {new Date(selectedInquiry.updated_at).toLocaleString()}</p>
                  {selectedInquiry.read_at && (
                    <p>Read: {new Date(selectedInquiry.read_at).toLocaleString()}</p>
                  )}
                  {selectedInquiry.responded_at && (
                    <p>Responded: {new Date(selectedInquiry.responded_at).toLocaleString()}</p>
                  )}
                </div>
              </div>
            </motion.div>
          ) : (
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center text-gray-500 dark:text-gray-400">
              Select an inquiry to view details
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
