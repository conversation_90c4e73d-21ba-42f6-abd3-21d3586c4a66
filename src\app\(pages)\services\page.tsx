import Link from "next/link";
import ContactForm from "@/components/forms/ContactForm";

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon: string;
}

export default function ServicesPage() {
  // Sample services data
  const services: Service[] = [
    {
      id: "web-development",
      title: "Web Development",
      description: "We create modern, responsive websites and web applications that deliver exceptional user experiences while achieving your business goals.",
      features: [
        "Custom website design and development",
        "E-commerce solutions",
        "Content management systems",
        "Progressive web applications",
        "Website maintenance and support",
        "Performance optimization"
      ],
      icon: "🌐",
    },
    {
      id: "software-development",
      title: "Software Development",
      description: "We build custom software solutions that automate processes, improve efficiency, and solve complex business challenges.",
      features: [
        "Custom business applications",
        "Enterprise software solutions",
        "Database design and development",
        "API development and integration",
        "Legacy system modernization",
        "Software maintenance and support"
      ],
      icon: "💻",
    },
    {
      id: "mobile-development",
      title: "Mobile Development",
      description: "We develop native and cross-platform mobile applications that provide seamless experiences across all devices.",
      features: [
        "iOS and Android app development",
        "Cross-platform development",
        "Mobile UI/UX design",
        "App store optimization",
        "App maintenance and updates",
        "Integration with backend systems"
      ],
      icon: "📱",
    },
    {
      id: "ui-ux-design",
      title: "UI/UX Design",
      description: "We create intuitive, engaging user interfaces and experiences that delight users and drive conversion.",
      features: [
        "User research and persona development",
        "Wireframing and prototyping",
        "Visual design and branding",
        "Usability testing",
        "Interaction design",
        "Design systems"
      ],
      icon: "🎨",
    },
    {
      id: "digital-strategy",
      title: "Digital Strategy",
      description: "We help businesses define and implement effective digital strategies to achieve their goals and stay ahead of the competition.",
      features: [
        "Digital transformation consulting",
        "Technology stack recommendations",
        "Product roadmap development",
        "Market and competitor analysis",
        "User journey mapping",
        "Digital growth strategies"
      ],
      icon: "📊",
    },
    {
      id: "maintenance-support",
      title: "Maintenance & Support",
      description: "We provide ongoing maintenance and support services to ensure your digital products remain secure, up-to-date, and performing optimally.",
      features: [
        "Regular updates and security patches",
        "Performance monitoring and optimization",
        "Bug fixes and troubleshooting",
        "Content updates",
        "Technical support",
        "Hosting and server management"
      ],
      icon: "🛠️",
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
              Our <span className="text-blue-600 dark:text-blue-400">Services</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Comprehensive technology solutions tailored to your business needs
            </p>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div 
                key={service.id}
                id={service.id}
                className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h2 className="text-2xl font-semibold mb-3">{service.title}</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">{service.description}</p>
                
                <h3 className="text-lg font-medium mb-3">What We Offer:</h3>
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-600 dark:text-blue-400 mr-2">✓</span>
                      <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Link 
                  href="/contact"
                  className="inline-block px-6 py-2 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
                >
                  Inquire Now
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-12 text-center">Our Development Process</h2>
            
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 top-0 bottom-0 w-1 bg-blue-200 dark:bg-blue-900"></div>
              
              {/* Timeline items */}
              <div className="space-y-12">
                {[
                  {
                    title: "Discovery & Planning",
                    description: "We begin by understanding your business goals, target audience, and project requirements. This phase includes research, strategy development, and project planning.",
                    step: "01",
                  },
                  {
                    title: "Design & Prototyping",
                    description: "Our designers create wireframes and interactive prototypes to visualize the solution before development begins, ensuring alignment with your expectations.",
                    step: "02",
                  },
                  {
                    title: "Development",
                    description: "Our development team brings the designs to life, writing clean, efficient code and implementing all required functionality according to best practices.",
                    step: "03",
                  },
                  {
                    title: "Testing & Quality Assurance",
                    description: "We rigorously test all aspects of your product to ensure it works flawlessly across all devices and browsers, fixing any issues that arise.",
                    step: "04",
                  },
                  {
                    title: "Deployment",
                    description: "Once approved, we deploy your product to the production environment, ensuring a smooth transition and minimal disruption.",
                    step: "05",
                  },
                  {
                    title: "Ongoing Support",
                    description: "We provide continued support and maintenance to keep your product running smoothly, implementing updates and improvements as needed.",
                    step: "06",
                  },
                ].map((phase, index) => (
                  <div 
                    key={index} 
                    className={`relative flex flex-col md:flex-row ${
                      index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                    } items-center`}
                  >
                    {/* Timeline dot */}
                    <div className="absolute left-0 md:left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-blue-600 dark:bg-blue-500 flex items-center justify-center text-white font-bold z-10">
                      {phase.step}
                    </div>
                    
                    {/* Content */}
                    <div className={`md:w-1/2 ${index % 2 === 0 ? 'md:pr-12' : 'md:pl-12'} pl-12 md:pl-0`}>
                      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                        <h3 className="text-xl font-semibold mb-2">{phase.title}</h3>
                        <p className="text-gray-600 dark:text-gray-300">{phase.description}</p>
                      </div>
                    </div>
                    
                    {/* Spacer for alignment */}
                    <div className="md:w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Technologies We Use</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-12">
              We leverage the latest technologies to deliver high-quality, scalable solutions
            </p>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
              {[
                "React", "Next.js", "Node.js", "TypeScript", "Python", "Django",
                "Flutter", "React Native", "AWS", "Firebase", "MongoDB", "PostgreSQL",
                "TailwindCSS", "GraphQL", "Docker", "Kubernetes", "Figma", "Adobe XD",
              ].map((tech, index) => (
                <div 
                  key={index} 
                  className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex items-center justify-center"
                >
                  <span className="font-medium">{tech}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-12 text-center">Frequently Asked Questions</h2>
            
            <div className="space-y-6">
              {[
                {
                  question: "How long does it typically take to complete a project?",
                  answer: "Project timelines vary depending on complexity and scope. A simple website might take 4-6 weeks, while a complex web application could take 3-6 months. During our initial consultation, we'll provide a more accurate timeline based on your specific requirements.",
                },
                {
                  question: "What is your pricing structure?",
                  answer: "We offer flexible pricing models including fixed-price projects, hourly rates, and retainer arrangements. The most suitable option depends on your project's nature and requirements. We provide detailed proposals with transparent pricing after understanding your needs.",
                },
                {
                  question: "Do you provide ongoing maintenance and support?",
                  answer: "Yes, we offer various maintenance and support packages to ensure your digital products remain secure, up-to-date, and performing optimally. These can include regular updates, security patches, content updates, and technical support.",
                },
                {
                  question: "Can you work with our existing systems and technologies?",
                  answer: "Absolutely. We have experience integrating with a wide range of existing systems and technologies. Our team will assess your current setup and develop solutions that seamlessly integrate with your existing infrastructure.",
                },
                {
                  question: "How do you ensure the security of the applications you develop?",
                  answer: "Security is a top priority in all our projects. We follow industry best practices for secure coding, implement appropriate authentication and authorization mechanisms, conduct security testing, and stay updated on the latest security vulnerabilities and patches.",
                },
              ].map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                  <details className="group">
                    <summary className="flex justify-between items-center p-6 cursor-pointer">
                      <h3 className="text-lg font-semibold">{faq.question}</h3>
                      <span className="text-blue-600 dark:text-blue-400 group-open:rotate-180 transition-transform">
                        ↓
                      </span>
                    </summary>
                    <div className="px-6 pb-6 pt-0">
                      <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                    </div>
                  </details>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Ready to Start Your Project?</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Tell us about your project requirements and we'll get back to you within 24 hours with a detailed proposal.
              </p>
            </div>

            <ContactForm
              type="service"
              title="Request a Service Quote"
              description="Fill out the form below and we'll provide you with a customized quote for your project."
            />
          </div>
        </div>
      </section>
    </div>
  );
}
