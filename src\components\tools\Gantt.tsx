'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { taskService } from '@/lib/services/taskService';
import { GanttTask } from '@/lib/types/task';
import { motion } from 'framer-motion';

export default function Gantt() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<GanttTask[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadGanttData();
    }
  }, [user]);

  const loadGanttData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const ganttTasks = await taskService.getGanttTasks(user.id);
      setTasks(ganttTasks);
    } catch (error) {
      console.error('Error loading Gantt data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateTimeScale = () => {
    if (tasks.length === 0) return { start: new Date(), end: new Date() };
    
    const dates = tasks.flatMap(task => [new Date(task.startDate), new Date(task.dueDate)]);
    const start = new Date(Math.min(...dates.map(d => d.getTime())));
    const end = new Date(Math.max(...dates.map(d => d.getTime())));
    
    return { start, end };
  };

  const { start, end } = calculateTimeScale();
  const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full overflow-x-auto"
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="min-w-[800px]">
          <div className="grid grid-cols-[200px,1fr] gap-4">
            <div className="font-semibold text-gray-700 dark:text-gray-300 p-2">Task</div>
            <div
              className="grid gap-0"
              style={{
                gridTemplateColumns: `repeat(${totalDays}, minmax(30px, 1fr))`
              }}
            >
              {Array.from({ length: totalDays }).map((_, index) => {
                const date = new Date(start);
                date.setDate(date.getDate() + index);
                return (
                  <div
                    key={index}
                    className="text-xs text-center text-gray-600 dark:text-gray-400 border-r border-gray-200 dark:border-gray-700 p-1"
                  >
                    {date.getDate()}
                  </div>
                );
              })}
            </div>
          </div>

          {tasks.map((task) => {
            const taskStart = new Date(task.startDate);
            const taskDays = task.duration;
            const startOffset = Math.floor(
              (taskStart.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
            );

            return (
              <div key={task.id} className="grid grid-cols-[200px,1fr] gap-4 mt-2">
                <div className="p-2 text-sm text-gray-700 dark:text-gray-300">
                  {task.title}
                </div>
                <div
                  className="grid gap-0"
                  style={{
                    gridTemplateColumns: `repeat(${totalDays}, minmax(30px, 1fr))`
                  }}
                >
                  <motion.div
                    className="absolute h-6 rounded-full bg-blue-500/50 dark:bg-blue-600/50"
                    style={{
                      left: `${(startOffset / totalDays) * 100}%`,
                      width: `${(taskDays / totalDays) * 100}%`
                    }}
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="h-full relative">
                      <div
                        className="absolute h-full bg-blue-500 dark:bg-blue-600 rounded-l-full"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                  </motion.div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </motion.div>
  );
}