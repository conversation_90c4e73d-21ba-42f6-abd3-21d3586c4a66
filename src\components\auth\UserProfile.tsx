'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import TaskTools from '@/components/tools/TaskTools';
import { useAuth } from '@/lib/auth/AuthContext';
import { supabase, db } from '@/lib/supabase';

interface UserProfileData {
  display_name: string;
  bio: string;
  company: string;
  position: string;
}

export default function UserProfile() {
  const { user, profile: userProfile } = useAuth();
  const [profile, setProfile] = useState<UserProfileData>({
    display_name: '',
    bio: '',
    company: '',
    position: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user && userProfile) {
      setProfile({
        display_name: userProfile.display_name || '',
        bio: (userProfile as any).bio || '',
        company: (userProfile as any).company || '',
        position: (userProfile as any).position || ''
      });
    }
  }, [user, userProfile]);

  const handleSave = async () => {
    if (!user?.id) return;

    setIsSaving(true);
    try {
      const profileData = {
        display_name: profile.display_name,
        bio: profile.bio,
        company: profile.company,
        position: profile.position
      };

      const result = await db.updateProfile(user.id, profileData);
      if (result) {
        setIsEditing(false);
      } else {
        console.error('Failed to update profile');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfile(prev => ({ ...prev, [name]: value }));
  };

  if (!user) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl mx-auto p-8 backdrop-blur-lg bg-white/10 dark:bg-gray-800/30 rounded-xl shadow-xl border border-white/20 dark:border-gray-700/30"
      >
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">Profile</h2>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-blue-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-blue-700/80 transition-all duration-300 hover:scale-105 hover:shadow-lg"
          >
            Edit Profile
          </button>
        ) : (
          <div className="space-x-2">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-4 py-2 bg-green-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-green-700/80 transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:opacity-50"
            >
              {isSaving ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="px-4 py-2 bg-gray-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-gray-700/80 transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      <motion.div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Display Name
          </label>
          <input
            type="text"
            name="display_name"
            value={profile.display_name}
            onChange={handleChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Bio
          </label>
          <textarea
            name="bio"
            value={profile.bio}
            onChange={handleChange}
            disabled={!isEditing}
            rows={3}
            className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Company
          </label>
          <input
            type="text"
            name="company"
            value={profile.company}
            onChange={handleChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Position
          </label>
          <input
            type="text"
            name="position"
            value={profile.position}
            onChange={handleChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
          />
        </motion.div>
      </motion.div>
      </motion.div>
      <TaskTools />
    </motion.div>
  );
}